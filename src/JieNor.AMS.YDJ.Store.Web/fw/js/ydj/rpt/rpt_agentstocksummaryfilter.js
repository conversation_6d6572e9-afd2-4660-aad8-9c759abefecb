///<reference path="/fw/js/consts.js" />
/*rpt_agentstocksummary_filter
@ sourceURL=/fw/js/ydj/stk/rpt_agentstocksummaryfilter.js
*/
; (function () {
    var rpt_agentstocksummaryfilter = (function (_super) {
        //构造函数
        var _child = function (args) {
            var that = this;
            _super.call(that, args);
        };
        //继承 BasePlugIn
        __extends(_child, _super);

        //在原型上定义所有实例共享成员，以便复用

        //属性（对于所有实例的属性值都是一样的情况下，在原型上定义）*********************************************************************************************************************************************************************************

        //方法（对于方法则都在原型上定义）*********************************************************************************************************************************************************************************

        //初始化事件
        _child.prototype.onInitialized = function () {
            var that = this;
            //that.Model.invokeFormOperation({
            //    id: 'tbGetCategory',
            //    opcode: 'GetCategory',
            //    param: {
            //        formId:"rpt_agentstocksummary"
            //    }
            //});
            debugger
            var param = {
                simpleData: {
                    formId: 'rpt_agentstocksummary',
                    fcategoryid: '',
                    domainType: 'dynamic'
                }
            };
            yiAjax.p('/bill/rpt_agentstocksummary?operationno=gettopcategory', param, function (r) {
                that.Model.unblockUI({ id: '#page#' });
                var objsId = "";
                var objsval = "";
                var res = r.operationResult;
                if (res.srvData) {

                    for (var i = 0; i < res.srvData.length; i++) {
                        objsId += res.srvData[i].fid + ",";
                        objsval += res.srvData[i].fname + ",";
                    }
                    objsId = objsId.substring(0, objsId.length - 1);
                    objsval = objsval.substring(0, objsval.length - 1);

                    var objs = { id: objsId, name: objsval };

                    that.Model.setValue({ id: 'fcategoryid', value: objs });
                }
            }, null, null, null, { async: false });

        }

        //获取基础资料字段动态过滤条件
        _child.prototype.onQueryFilterString = function (e) {
            var that = this;
            if (!e.id) return;
            switch (e.id.toLowerCase()) {
                case 'fcategoryid':
                    e.result.filterString = "  fparentid=(select  fid from ser_ydj_category where fname='产品类') and fforbidstatus='0' and fmainorgid='821347239912935425' or fid='Other' ";
                    break;
            }
        };



        _child.prototype.onFieldValueChanged = function (e) {
            if (!e || !e.opcode) return;
            var that = this;

            debugger
            switch (e.id.toLowerCase()) {
                case 'fcategoryid':
                    if (srvData != null) {
                        if (e.value && e.value.id) {
                        }
                    }
                    break;
            }
        };

        _child.prototype.onBeforeDoOperation = function (e) {
            if (!e || !e.opcode) return;
            var that = this;

            switch (e.id.toLowerCase()) {
                case 'fcategoryid':
                    if (srvData != null) {
                        if (e.value && e.value.id) {
                        }
                    }
                    break;
            }
        };

        _child.prototype.onBeforeSetValue = function (e) {
            if (!e || !e.opcode) return;
            var that = this;
            debugger
            switch (e.id.toLowerCase()) {
                case 'fcategoryid':
                    if (srvData != null) {
                        if (e.value && e.value.id) {
                        }
                    }
                    break;
            }
        };

        //操作成功后触发的事件
        _child.prototype.onAfterDoOperation = function (e) {
            if (!e || !e.opcode) return;
            var that = this;
            var isSuccess = e.result.operationResult.isSuccess;
            var srvData = e.result.operationResult.srvData;
            switch (e.opcode) {
                case 'querydata':
                    if (srvData != null) {
                        var objs = { id: 'fcategoryid_01,fcategoryid_02,fcategoryid_03', name: '床垫类,床架类,其他类' };
                        that.Model.setValue({ id: 'fcategoryid', value: objs });
                        //that.fnotoutspotratio = srvData.notOutSpotRatio;
                        //that.foutspotratio = srvData.outspotratio;
                    }
                    break;
                case 'updatedata':
                    that.Model.close();
                    yiDialog.mt({ msg: '报表正在加速汇总统计中，请耐心等待几分钟后，再回来刷新查看！', skinseq: 1 });
                    break;
            }
        };

        //自身特有的操作
        _child.prototype.onMenuItemClick = function (e) {
            var that = this;
            switch (e.opcode.toLowerCase()) {
                //取消
                case 'costcancel':
                    e.result = true;
                    that.Model.close();
                    break;
                //立即收款
                case 'costconfirm':
                    e.result = true;
                    debugger
                    var fcategoryid = that.Model.getValue({ id: 'fcategoryid' });
                    if (fcategoryid.id.indexOf('Other') < 0) {
                        yiDialog.mt({ msg: '对不起，其他类为固定项，禁止删除！', skinseq: 3 });
                        var ids = fcategoryid.id;
                        var names = fcategoryid.name;
                        var objs = { id: "", name: "" };
                        that.Model.setValue({ id: 'fcategoryid', value: objs });
                        objs = { id: ids + ",Other", name: names + ",其他类" };
                        that.Model.setValue({ id: 'fcategoryid', value: objs });

                        return;
                    }
                    that.Model.invokeFormOperation({
                        id: 'updateData',
                        opcode: 'updateData',
                        param: {
                            'formId': 'rpt_agentstocksummary',
                            'fcategoryid': fcategoryid.id
                        }
                    });
                    //yiDialog.c("是否立即刷新报表数据？确定刷新则需耐心等待几分钟。", function () {
                    //    that.Model.invokeFormOperation({
                    //        id: 'updateData',
                    //        opcode: 'updateData',
                    //        param: {
                    //            'formId': 'rpt_agentstocksummary',
                    //            'fcategoryid': fcategoryid.id
                    //        }
                    //    });
                    //}, function () {
                    //    e.result = true;
                    //}, '提示');

                    break;
            }
        };


        return _child;
    })(ListReportPlugin);
    window.rpt_agentstocksummaryfilter = window.rpt_agentstocksummaryfilter || rpt_agentstocksummaryfilter;
})();